/*
 * Copyright (c) 2019 Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 * EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

/*
 *  ======== usb.json ========
 */

{
    "type"        : "USB",
    "displayName" : "USB",
    "signals"     : {
        "DM": { "type": "USB_DM" },
        "DP": { "type": "USB_DP" },
        "ID": { "type": "USB_ID" },
        "VBUS": { "type": "USB_VBUS" },
        "EPEN": { "type": "USB_EPEN" },
        "PFLT": { "type": "USB_PFLT" },
        "CLK": { "type": "USB_CLK" },
        "D0": { "type": "USB_D0" },
        "D1": { "type": "USB_D1" },
        "D2": { "type": "USB_D2" },
        "D3": { "type": "USB_D3" },
        "D4": { "type": "USB_D4" },
        "D5": { "type": "USB_D5" },
        "D6": { "type": "USB_D6" },
        "D7": { "type": "USB_D7" },
        "DIR": { "type": "USB_DIR" },
        "NXT": { "type": "USB_NXT" },
        "STP": { "type": "USB_STP" }
    }
}
