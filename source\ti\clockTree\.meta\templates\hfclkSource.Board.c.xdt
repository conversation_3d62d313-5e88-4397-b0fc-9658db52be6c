%%{
/*
 * Copyright (c) 2022, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

/*
 *  ======== hfclkSource.Board.c.xdt ========
 */

    let Common = system.getScript("/ti/driverlib/Common.js");

    let mod = system.modules["/ti/clockTree/mux.js"];

    let exhfMuxInst = _.find(mod.$instances, ['$name', 'EXHFMUX']);

    let modPF = system.modules["/ti/clockTree/pinFunction.js"];

    let hfxtInst = _.find(modPF.$instances, ['$name','HFXT']);
    let hfextInst = _.find(modPF.$instances, ['$name', 'HFCLKEXT']);

    if(_.isUndefined(exhfMuxInst) || _.isUndefined(hfextInst)) {
        throw 'Not all HFCLKEXT elements are defined appropriately';
    }

    let hfclkStr = "";

    /* Traverse the MCLK table to look for clock source */

    // uses external digital
    if(_.endsWith(exhfMuxInst.inputSelect, "XTAL")){
        /* Validation TODO: LFXT will need to be enabled in order for this to make sense */
        if(hfxtInst.enable){
            hfclkStr = "DL_SYSCTL_setHFCLKSourceHFXTParams(" + hfxtInst.HFXTRange +
                "," + hfxtInst.HFXTStartup + ", " + hfxtInst.HFCLKMonitor + ");";
        }
    } else {
        if(hfextInst.enable){
            hfclkStr = "DL_SYSCTL_setHFCLKSourceHFCLKIN();";
        }
    }

%%}
%
    `hfclkStr`
%
