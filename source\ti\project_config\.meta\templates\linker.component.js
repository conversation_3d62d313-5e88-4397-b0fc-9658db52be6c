/*
 * Copyright (c) 2023, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, IN<PERSON>DENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

/*
 *  ======== drivers.component.js ========
 */

"use strict";

let Common = system.getScript("/ti/driverlib/Common.js");
let MigrationCommon = system.getScript("/ti/project_config/Common.js");
let references = system.getScript("/ti/project_config/references/ProjectConfigReferences.js");

let topModules;
let alwaysPresent;
let displayName = "Linker Command";
let description = "Linker Command";

let migrationModules = [];


topModules = [
    {
        displayName: displayName,
        description: description,
        modules: migrationModules,
        categories: [
            // {
            //     displayName: "Subcategory 01",
            //     modules: driversTestList
            // },
        ]
    },
];

alwaysPresent = []

let templates = [
    {
        "name": "/ti/project_config/linker/device_linker.cmd.xdt",
        "outputPath": "device_linker.cmd",
        "alwaysRun": false
    },
    {
        "name": "/ti/project_config/linker/device_linker.lds.xdt",
        "outputPath": "device_linker.lds",
        "alwaysRun": false
    },
    {
        "name": "/ti/project_config/linker/device_linker.icf.xdt",
        "outputPath": "device_linker.icf",
        "alwaysRun": false
    },
    {
        "name": "/ti/project_config/linker/device_linker.sct.xdt",
        "outputPath": "device_linker.sct",
        "alwaysRun": false
    },
];

exports = {
    displayName: displayName,
    topModules: topModules,
    templates: templates,
    alwaysPresent: alwaysPresent,
};
