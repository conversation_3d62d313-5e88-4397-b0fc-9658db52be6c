%%{
/*
 * Copyright (c) 2022, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

/*
 *  ======== lfclkSource.Board.c.xdt ========
 */

    let Common = system.getScript("/ti/driverlib/Common.js");

    let mod = system.modules["/ti/clockTree/mux.js"];

    let exlfMuxInst = _.find(mod.$instances, ['$name', 'EXLFMUX']);
    let lfxtMuxInst = _.find(mod.$instances, ['$name', 'LFXTMUX']);

    mod = system.modules["/ti/clockTree/pinFunction.js"];
    let lfxtInst = _.find(mod.$instances, ['$name','LFXT']);
    let exlfInst = _.find(mod.$instances, ['$name','LFCLKEXT']);

    if(_.isUndefined(exlfMuxInst) || _.isUndefined(lfxtMuxInst) ||
       _.isUndefined(lfxtInst) || _.isUndefined(exlfInst)) {
        if(Common.isDeviceM0G()){
            throw 'Not all LFCLK elements are defined appropriately';
        }
    } else if ((_.isUndefined(exlfMuxInst) ||  _.isUndefined(exlfInst))) {
        if(Common.isDeviceM0C()){
            throw 'Not all LFCLK elements are defined appropriately';
        } else {
            return; // LFCLK not supported
        }
    }

    let lfclkStr = ""

    if(Common.isDeviceM0G() || Common.isDeviceFamily_PARENT_MSPM0L122X_L222X()){

        /* Traverse the MCLK table to look for clock source */
        if(_.endsWith(lfxtMuxInst.inputSelect, "FALSE")){
            // use LFOSC, no code generated
            return;
        } else {
            // uses external digital
            if(_.endsWith(exlfMuxInst.inputSelect, "XTAL")){
                /* don't want to generate code unless it's actually going to get used. Check enabling */
                if(lfxtInst.enable){
                    lfclkStr = "DL_SYSCTL_setLFCLKSourceLFXT((DL_SYSCTL_LFCLKConfig *) &gLFCLKConfig);";
                }
            } else {
                if(exlfInst.enable){
                    lfclkStr = "DL_SYSCTL_setLFCLKSourceEXLF();";
                }
            }
        }

    } else if(Common.isDeviceM0C()) {
        if(_.endsWith(exlfMuxInst.inputSelect, "EXT")){
            if(exlfInst.enable){
                lfclkStr = "DL_SYSCTL_setLFCLKSourceEXLF();";
            }
        } else {
            // LFOSC used; do nothing
        }
    }

%%}
%
    `lfclkStr`
%
