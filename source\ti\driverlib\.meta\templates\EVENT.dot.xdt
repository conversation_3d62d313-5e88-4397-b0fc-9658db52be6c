%%{
/*
 * Copyright (c) 2023, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

    /* get ti/drivers common utility functions */
    let Common = system.getScript("/ti/driverlib/Common.js");

    let MAX_NUM_EVENT_CHANNELS;
    let SPLITTER_EVENT_CHANNELS = [];
    if (Common.isDeviceM0L() || Common.isDeviceM0C())
    {
        /*
         * M0L devices support 4 event channels (0, 1, 2, and 3). Event channel
         * 3 is a splitter (1:2) channel
         */
        MAX_NUM_EVENT_CHANNELS = 4;
        SPLITTER_EVENT_CHANNELS = [3];
    }
    else
    {
        /*
         * M0G devices support 16 event channels (0 to 15). Event channels
         * 12-15 are splitters
         */
        MAX_NUM_EVENT_CHANNELS = 16;
        SPLITTER_EVENT_CHANNELS = [12, 13, 14, 15];
    }

    /* localize the object names with this device ID */
    let devId = system.deviceData.deviceId;
    let part = system.deviceData.part;
    let boardName = Common.boardName();
    let event = system.modules["/ti/driverlib/EVENT"];
    if(!event){
        // if no event, then there's nothing to generate
        return;
    }
    let inst = event.$static;
%%}


digraph H {

  parent [
   shape=plaintext
   label=<
     <table border='1' cellborder='1'>
       <tr>
%   for(let chanIdx = 1; chanIdx < MAX_NUM_EVENT_CHANNELS; chanIdx ++){
%        let pub = inst["channel"+chanIdx+"Pub"] != "";
%        let sub = inst["channel"+chanIdx+"Sub"] != "";
%        let color = "grey";
%        if(pub && sub){
%           color = "green";
%        } else if (pub || sub){
%           color = "red";
%        }
%        if (SPLITTER_EVENT_CHANNELS.includes(chanIdx)) {
            <td bgcolor="black" port='chan_`chanIdx`'><font color="`color`">Channel `chanIdx`</font></td>
%        } else {
            <td bgcolor="`color`" port='chan_`chanIdx`'>Channel `chanIdx`</td>
%        }
%   }
         </tr>
     </table>
  >];

%   for(let chanIdx = 1; chanIdx < MAX_NUM_EVENT_CHANNELS; chanIdx ++){
%        if(inst["channel"+chanIdx+"Pub"] != "") {
pub_chan_`chanIdx` [
    shape=plaintext
    label=<
    <table border='1'  cellborder='0'>
      <tr><td>`inst["channel"+chanIdx+"Pub"]`</td></tr>
    </table>
    >];
pub_chan_`chanIdx` -> parent: chan_`chanIdx`;

%        }
%        if(inst["channel"+chanIdx+"Sub"] != "") {
%           let j = 0;
%           while(j < inst["channel"+chanIdx+"Sub"].length){
sub_chan_`chanIdx`_`j` [
  shape=plaintext
  label=<
    <table border='1'  cellborder='0'>
      <tr><td>`inst["channel"+chanIdx+"Sub"][j]`</td></tr>
    </table>
  >];
parent: chan_`chanIdx` -> sub_chan_`chanIdx`_`j`;
%             j++;
%           }
%        }
%
%   }

}
