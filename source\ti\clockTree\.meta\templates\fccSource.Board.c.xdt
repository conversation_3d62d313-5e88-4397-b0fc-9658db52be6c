%%{
/*
 * Copyright (c) 2022, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

/*
 *  ======== fccSource.Board.c.xdt ========
 */

    let Common = system.getScript("/ti/driverlib/Common.js");

    let mod = system.modules["/ti/clockTree/mux.js"];

    let fccMuxInst = _.find(mod.$instances, ['$name', 'FCCSELCLKMUX']);
    let fcctrigMuxInst = _.find(mod.$instances, ['$name', 'FCCTRIGMUX']);

    mod = system.modules["/ti/clockTree/pinFunction.js"];
    let fccinInst = _.find(mod.$instances, ['$name', 'FCCEXT']);

    mod = system.modules["/ti/clockTree/fcc.js"];

    let fccInst = _.find(mod.$instances, ['$name','FCC']);

    if(_.isUndefined(fccMuxInst) || _.isUndefined(fccInst) ||
       _.isUndefined(fccinInst)  ||
       (Common.isDeviceM0G() && _.isUndefined(fcctrigMuxInst))) {
        throw 'Not all FCC elements are defined appropriately';
    }

    let fccStr = "";

    if(fccInst.enable) {
        /* Traverse to find the trigger source and clk source */
        let fccClkSrc = fccMuxInst.inputSelect.slice(_.indexOf(fccMuxInst.inputSelect, "_") + 1);
        let fccTrigSrc = "FCC_IN";
        if(!_.isUndefined(fcctrigMuxInst)){
            fccTrigSrc = fcctrigMuxInst.inputSelect.slice(_.indexOf(fcctrigMuxInst.inputSelect, "_") + 1);
        }
        fccStr += "DL_SYSCTL_configFCC(DL_SYSCTL_FCC_TRIG_TYPE_" + fccInst.fccTrigLvl +
                ", DL_SYSCTL_FCC_TRIG_SOURCE_" + fccTrigSrc +
                ", DL_SYSCTL_FCC_CLOCK_SOURCE_" + fccClkSrc + ");"
        if(fccInst.fccTrigLvl == "RISE_RISE") {
            fccStr += "\n\tDL_SYSCTL_setFCCPeriods(DL_SYSCTL_FCC_TRIG_CNT_" + fccInst.fccPeriods + ");"
        }
    }


%%}
%
    `fccStr`
%
